# 阶段3：功能增强优化 - 中间件集成与性能提升 (2-3周)

## 🎯 本阶段目标
- 集成Elasticsearch实现全文搜索
- 集成RabbitMQ实现异步处理
- 优化Redis缓存策略
- 实现文档自动处理流程
- 提升系统性能和用户体验

## 📚 需要学习的技术

### 1. Elasticsearch搜索引擎
- **学习资源**：
  - [Elasticsearch官方文档](https://www.elastic.co/guide/en/elasticsearch/reference/current/index.html)
  - [Spring Data Elasticsearch](https://docs.spring.io/spring-data/elasticsearch/docs/current/reference/html/)
- **核心概念**：
  - 索引(Index)、文档(Document)、映射(Mapping)
  - 查询DSL、聚合分析
  - 分词器、相关性评分

### 2. RabbitMQ消息队列
- **学习资源**：
  - [RabbitMQ官方教程](https://www.rabbitmq.com/tutorials/tutorial-one-java.html)
  - [Spring AMQP文档](https://docs.spring.io/spring-amqp/docs/current/reference/html/)
- **核心概念**：
  - 交换机(Exchange)、队列(Queue)、路由键(Routing Key)
  - 消息持久化、确认机制
  - 死信队列、延迟队列

### 3. Redis高级应用
- **学习资源**：
  - [Redis官方文档](https://redis.io/documentation)
  - [Spring Cache抽象](https://docs.spring.io/spring-framework/docs/current/reference/html/integration.html#cache)
- **核心概念**：
  - 缓存策略、缓存穿透/击穿/雪崩
  - 分布式锁、限流算法
  - 数据结构高级应用

## 🛠️ 开发步骤

### 步骤1：Elasticsearch集成

#### 1.1 添加依赖
```xml
<!-- backend/pom.xml 添加ES依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
</dependency>

<dependency>
    <groupId>co.elastic.clients</groupId>
    <artifactId>elasticsearch-java</artifactId>
</dependency>
```

#### 1.2 ES配置
```yaml
# backend/src/main/resources/application.yml
spring:
  elasticsearch:
    uris: http://localhost:9200
    username: 
    password: 
    connection-timeout: 10s
    socket-timeout: 30s

# 自定义ES配置
elasticsearch:
  index:
    document: knowledge_documents
    vector: knowledge_vectors
  settings:
    number_of_shards: 1
    number_of_replicas: 0
```

#### 1.3 文档索引实体
```java
// backend/src/main/java/com/knowledge/entity/DocumentIndex.java
package com.knowledge.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Document(indexName = "knowledge_documents")
@Setting(settingPath = "elasticsearch/document-settings.json")
@Mapping(mappingPath = "elasticsearch/document-mapping.json")
public class DocumentIndex {
    
    @Id
    private String id;
    
    @Field(type = FieldType.Long)
    private Long documentId;
    
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String title;
    
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String content;
    
    @Field(type = FieldType.Keyword)
    private String contentType;
    
    @Field(type = FieldType.Long)
    private Long userId;
    
    @Field(type = FieldType.Keyword)
    private List<String> tags;
    
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    private LocalDateTime createdAt;
    
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second)
    private LocalDateTime updatedAt;
    
    // 向量字段（用于语义搜索）
    @Field(type = FieldType.Dense_Vector, dims = 384)
    private float[] vector;
    
    @Field(type = FieldType.Text)
    private String summary;
    
    @Field(type = FieldType.Integer)
    private Integer chunkIndex;
    
    @Field(type = FieldType.Text)
    private String chunkContent;
}
```

#### 1.4 ES映射配置
```json
// backend/src/main/resources/elasticsearch/document-mapping.json
{
  "properties": {
    "documentId": {
      "type": "long"
    },
    "title": {
      "type": "text",
      "analyzer": "ik_max_word",
      "search_analyzer": "ik_smart",
      "fields": {
        "keyword": {
          "type": "keyword",
          "ignore_above": 256
        }
      }
    },
    "content": {
      "type": "text",
      "analyzer": "ik_max_word",
      "search_analyzer": "ik_smart"
    },
    "contentType": {
      "type": "keyword"
    },
    "userId": {
      "type": "long"
    },
    "tags": {
      "type": "keyword"
    },
    "createdAt": {
      "type": "date",
      "format": "yyyy-MM-dd HH:mm:ss"
    },
    "updatedAt": {
      "type": "date",
      "format": "yyyy-MM-dd HH:mm:ss"
    },
    "vector": {
      "type": "dense_vector",
      "dims": 384,
      "index": true,
      "similarity": "cosine"
    },
    "summary": {
      "type": "text",
      "analyzer": "ik_max_word"
    },
    "chunkIndex": {
      "type": "integer"
    },
    "chunkContent": {
      "type": "text",
      "analyzer": "ik_max_word",
      "search_analyzer": "ik_smart"
    }
  }
}
```

#### 1.5 搜索服务
```java
// backend/src/main/java/com/knowledge/service/SearchService.java
package com.knowledge.service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.knowledge.dto.SearchRequest;
import com.knowledge.dto.SearchResult;
import com.knowledge.entity.DocumentIndex;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SearchService {
    
    private final ElasticsearchClient elasticsearchClient;
    private static final String DOCUMENT_INDEX = "knowledge_documents";
    
    public SearchResult searchDocuments(SearchRequest request) {
        try {
            // 构建查询
            Query query = buildQuery(request);
            
            // 执行搜索
            SearchRequest esRequest = SearchRequest.of(s -> s
                .index(DOCUMENT_INDEX)
                .query(query)
                .from(request.getPage() * request.getSize())
                .size(request.getSize())
                .highlight(h -> h
                    .fields("title", hf -> hf.preTags("<mark>").postTags("</mark>"))
                    .fields("content", hf -> hf.preTags("<mark>").postTags("</mark>"))
                )
            );
            
            SearchResponse<DocumentIndex> response = elasticsearchClient.search(esRequest, DocumentIndex.class);
            
            // 处理结果
            List<SearchResult.SearchItem> items = response.hits().hits().stream()
                .map(this::convertToSearchItem)
                .collect(Collectors.toList());
            
            return SearchResult.builder()
                .items(items)
                .total(response.hits().total().value())
                .page(request.getPage())
                .size(request.getSize())
                .took(response.took())
                .build();
            
        } catch (Exception e) {
            log.error("搜索失败", e);
            throw new RuntimeException("搜索失败: " + e.getMessage());
        }
    }
    
    private Query buildQuery(SearchRequest request) {
        if (request.getKeyword() != null && !request.getKeyword().trim().isEmpty()) {
            // 关键词搜索
            return Query.of(q -> q
                .multiMatch(m -> m
                    .query(request.getKeyword())
                    .fields("title^2", "content", "summary")
                    .type(TextQueryType.BestFields)
                    .fuzziness("AUTO")
                )
            );
        } else {
            // 匹配所有
            return Query.of(q -> q.matchAll(m -> m));
        }
    }
    
    private SearchResult.SearchItem convertToSearchItem(Hit<DocumentIndex> hit) {
        DocumentIndex doc = hit.source();
        
        return SearchResult.SearchItem.builder()
            .id(doc.getDocumentId())
            .title(doc.getTitle())
            .content(getHighlightedContent(hit))
            .contentType(doc.getContentType())
            .tags(doc.getTags())
            .score(hit.score())
            .createdAt(doc.getCreatedAt())
            .build();
    }
    
    private String getHighlightedContent(Hit<DocumentIndex> hit) {
        if (hit.highlight() != null && hit.highlight().get("content") != null) {
            return String.join("...", hit.highlight().get("content"));
        }
        return hit.source().getContent().substring(0, Math.min(200, hit.source().getContent().length())) + "...";
    }
    
    public void indexDocument(DocumentIndex document) {
        try {
            elasticsearchClient.index(i -> i
                .index(DOCUMENT_INDEX)
                .id(document.getId())
                .document(document)
            );
            log.info("文档索引成功: {}", document.getId());
        } catch (Exception e) {
            log.error("文档索引失败", e);
            throw new RuntimeException("文档索引失败: " + e.getMessage());
        }
    }
    
    public void deleteDocument(String documentId) {
        try {
            elasticsearchClient.delete(d -> d
                .index(DOCUMENT_INDEX)
                .id(documentId)
            );
            log.info("文档删除成功: {}", documentId);
        } catch (Exception e) {
            log.error("文档删除失败", e);
            throw new RuntimeException("文档删除失败: " + e.getMessage());
        }
    }
}
```

### 步骤2：RabbitMQ消息队列集成

#### 2.1 添加依赖
```xml
<!-- backend/pom.xml 添加RabbitMQ依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-amqp</artifactId>
</dependency>
```

#### 2.2 RabbitMQ配置
```yaml
# backend/src/main/resources/application.yml
spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: admin
    password: admin123
    virtual-host: /
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true
    listener:
      simple:
        acknowledge-mode: manual
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          multiplier: 2
```

#### 2.3 队列配置
```java
// backend/src/main/java/com/knowledge/config/RabbitConfig.java
package com.knowledge.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitConfig {
    
    // 交换机
    public static final String KNOWLEDGE_EXCHANGE = "knowledge.topic";
    
    // 队列
    public static final String DOCUMENT_PROCESS_QUEUE = "document.process";
    public static final String VECTOR_GENERATE_QUEUE = "vector.generate";
    public static final String NOTIFICATION_QUEUE = "notification.user";
    public static final String ANALYTICS_QUEUE = "analytics.event";
    
    // 路由键
    public static final String DOCUMENT_PROCESS_ROUTING_KEY = "document.process";
    public static final String VECTOR_GENERATE_ROUTING_KEY = "vector.generate";
    public static final String NOTIFICATION_ROUTING_KEY = "notification.user";
    public static final String ANALYTICS_ROUTING_KEY = "analytics.event";
    
    @Bean
    public TopicExchange knowledgeExchange() {
        return new TopicExchange(KNOWLEDGE_EXCHANGE, true, false);
    }
    
    @Bean
    public Queue documentProcessQueue() {
        return QueueBuilder.durable(DOCUMENT_PROCESS_QUEUE)
            .withArgument("x-dead-letter-exchange", KNOWLEDGE_EXCHANGE + ".dlx")
            .withArgument("x-dead-letter-routing-key", "document.process.failed")
            .build();
    }
    
    @Bean
    public Queue vectorGenerateQueue() {
        return QueueBuilder.durable(VECTOR_GENERATE_QUEUE).build();
    }
    
    @Bean
    public Queue notificationQueue() {
        return QueueBuilder.durable(NOTIFICATION_QUEUE).build();
    }
    
    @Bean
    public Queue analyticsQueue() {
        return QueueBuilder.durable(ANALYTICS_QUEUE).build();
    }
    
    @Bean
    public Binding documentProcessBinding() {
        return BindingBuilder.bind(documentProcessQueue())
            .to(knowledgeExchange())
            .with(DOCUMENT_PROCESS_ROUTING_KEY);
    }
    
    @Bean
    public Binding vectorGenerateBinding() {
        return BindingBuilder.bind(vectorGenerateQueue())
            .to(knowledgeExchange())
            .with(VECTOR_GENERATE_ROUTING_KEY);
    }
    
    @Bean
    public Binding notificationBinding() {
        return BindingBuilder.bind(notificationQueue())
            .to(knowledgeExchange())
            .with(NOTIFICATION_ROUTING_KEY);
    }
    
    @Bean
    public Binding analyticsBinding() {
        return BindingBuilder.bind(analyticsQueue())
            .to(knowledgeExchange())
            .with(ANALYTICS_ROUTING_KEY);
    }
    
    @Bean
    public Jackson2JsonMessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }
    
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(messageConverter());
        template.setMandatory(true);
        return template;
    }
    
    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
            ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter());
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        return factory;
    }
}
```

#### 2.4 消息生产者和消费者
```java
// backend/src/main/java/com/knowledge/service/MessageProducer.java
package com.knowledge.service;

import com.knowledge.config.RabbitConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MessageProducer {

    private final RabbitTemplate rabbitTemplate;

    public void sendDocumentProcessMessage(Long documentId, String filePath, String fileType) {
        try {
            DocumentProcessMessage message = DocumentProcessMessage.builder()
                .documentId(documentId)
                .filePath(filePath)
                .fileType(fileType)
                .timestamp(System.currentTimeMillis())
                .build();

            rabbitTemplate.convertAndSend(
                RabbitConfig.KNOWLEDGE_EXCHANGE,
                RabbitConfig.DOCUMENT_PROCESS_ROUTING_KEY,
                message
            );
            log.info("文档处理消息发送成功: {}", documentId);
        } catch (Exception e) {
            log.error("文档处理消息发送失败", e);
            throw new RuntimeException("消息发送失败: " + e.getMessage());
        }
    }
}
```

### 步骤3：Redis缓存优化

#### 3.1 缓存配置
```java
// backend/src/main/java/com/knowledge/config/CacheConfig.java
package com.knowledge.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);

        template.setValueSerializer(serializer);
        template.setHashValueSerializer(serializer);
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());

        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1))
            .disableCachingNullValues();

        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        cacheConfigurations.put("user", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        cacheConfigurations.put("document", defaultConfig.entryTtl(Duration.ofHours(2)));
        cacheConfigurations.put("search", defaultConfig.entryTtl(Duration.ofMinutes(10)));
        cacheConfigurations.put("qa", defaultConfig.entryTtl(Duration.ofHours(1)));

        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(defaultConfig)
            .withInitialCacheConfigurations(cacheConfigurations)
            .build();
    }
}
```

#### 3.2 缓存服务应用
```java
// 在DocumentService中应用缓存
@Service
@RequiredArgsConstructor
public class DocumentService {

    @Cacheable(value = "document", key = "#id")
    public Document getDocument(Long id) {
        return documentMapper.selectById(id);
    }

    @CacheEvict(value = "document", key = "#document.id")
    public void updateDocument(Document document) {
        documentMapper.updateById(document);
    }

    @CacheEvict(value = "document", key = "#id")
    public void deleteDocument(Long id) {
        documentMapper.deleteById(id);
    }
}
```

### 步骤4：前端搜索功能增强

#### 4.1 搜索组件
```typescript
// frontend/src/components/SearchBox.tsx
import React, { useState, useCallback } from 'react';
import { Input, AutoComplete, Spin } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { debounce } from 'lodash';
import { searchService } from '../services/searchService';

interface SearchBoxProps {
  onSearch: (keyword: string) => void;
  placeholder?: string;
}

const SearchBox: React.FC<SearchBoxProps> = ({ onSearch, placeholder = "搜索文档..." }) => {
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const debouncedSearch = useCallback(
    debounce(async (value: string) => {
      if (!value.trim()) {
        setOptions([]);
        return;
      }

      setLoading(true);
      try {
        const result = await searchService.searchSuggestions(value);
        const suggestions = result.map((item: any) => ({
          value: item.title,
          label: (
            <div>
              <div style={{ fontWeight: 'bold' }}>{item.title}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {item.content.substring(0, 50)}...
              </div>
            </div>
          ),
        }));
        setOptions(suggestions);
      } catch (error) {
        console.error('搜索建议失败:', error);
      } finally {
        setLoading(false);
      }
    }, 300),
    []
  );

  const handleSearch = (value: string) => {
    onSearch(value);
  };

  const handleSelect = (value: string) => {
    onSearch(value);
  };

  return (
    <AutoComplete
      options={options}
      onSelect={handleSelect}
      onSearch={debouncedSearch}
      style={{ width: '100%' }}
    >
      <Input.Search
        placeholder={placeholder}
        enterButton={<SearchOutlined />}
        size="large"
        onSearch={handleSearch}
        suffix={loading ? <Spin size="small" /> : null}
      />
    </AutoComplete>
  );
};

export default SearchBox;
```

#### 4.2 搜索页面
```typescript
// frontend/src/pages/Search.tsx
import React, { useState, useEffect } from 'react';
import { Card, List, Tag, Pagination, Spin, Empty } from 'antd';
import { FileTextOutlined, ClockCircleOutlined } from '@ant-design/icons';
import SearchBox from '../components/SearchBox';
import { searchService } from '../services/searchService';

const Search: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  const handleSearch = async (keyword: string, page = 1) => {
    if (!keyword.trim()) return;

    setLoading(true);
    try {
      const result = await searchService.searchDocuments({
        keyword,
        page: page - 1,
        size: pageSize,
      });
      setSearchResult(result);
      setCurrentPage(page);
    } catch (error) {
      console.error('搜索失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    if (searchResult?.keyword) {
      handleSearch(searchResult.keyword, page);
    }
  };

  return (
    <div>
      <Card>
        <SearchBox onSearch={(keyword) => handleSearch(keyword, 1)} />
      </Card>

      {loading && (
        <Card style={{ marginTop: 16, textAlign: 'center' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>搜索中...</div>
        </Card>
      )}

      {searchResult && !loading && (
        <Card
          style={{ marginTop: 16 }}
          title={`搜索结果 (共找到 ${searchResult.total} 条)`}
        >
          {searchResult.items.length > 0 ? (
            <>
              <List
                dataSource={searchResult.items}
                renderItem={(item: any) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<FileTextOutlined style={{ fontSize: 24, color: '#1890ff' }} />}
                      title={
                        <div>
                          <span dangerouslySetInnerHTML={{ __html: item.title }} />
                          <span style={{ marginLeft: 8, fontSize: '12px', color: '#999' }}>
                            评分: {item.score?.toFixed(2)}
                          </span>
                        </div>
                      }
                      description={
                        <div>
                          <div
                            style={{ marginBottom: 8 }}
                            dangerouslySetInnerHTML={{ __html: item.content }}
                          />
                          <div>
                            {item.tags?.map((tag: string) => (
                              <Tag key={tag} color="blue">{tag}</Tag>
                            ))}
                            <span style={{ marginLeft: 8, color: '#999' }}>
                              <ClockCircleOutlined /> {item.createdAt}
                            </span>
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />

              <Pagination
                current={currentPage}
                total={searchResult.total}
                pageSize={pageSize}
                onChange={handlePageChange}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
                style={{ marginTop: 16, textAlign: 'center' }}
              />
            </>
          ) : (
            <Empty description="没有找到相关文档" />
          )}
        </Card>
      )}
    </div>
  );
};

export default Search;
```

## 🎯 阶段完成检查清单

- [ ] Elasticsearch集成完成，支持全文搜索
- [ ] 中文分词器配置正确
- [ ] 搜索建议和高亮功能实现
- [ ] RabbitMQ消息队列集成完成
- [ ] 异步文档处理流程实现
- [ ] 消息确认和重试机制配置
- [ ] Redis缓存策略优化
- [ ] 多级缓存配置完成
- [ ] 前端搜索功能增强
- [ ] 搜索结果分页和高亮显示
- [ ] 系统性能显著提升

## 🚀 下一步

恭喜完成阶段3！你的系统现在具备了企业级的性能和功能。接下来可以考虑：

1. **高级AI功能** - 模型微调、知识图谱构建
2. **监控运维** - 集成APM监控、日志分析
3. **微服务拆分** - 按业务域拆分服务
4. **容器化部署** - Docker化和K8s部署

你已经掌握了现代企业级应用开发的核心技术栈！🎉

## 💡 性能优化建议

1. **数据库优化**：添加合适的索引，使用读写分离
2. **缓存策略**：合理设置缓存过期时间，避免缓存雪崩
3. **异步处理**：耗时操作全部异步化，提升响应速度
4. **搜索优化**：使用ES的聚合功能，实现智能推荐
5. **前端优化**：使用虚拟滚动、懒加载等技术
