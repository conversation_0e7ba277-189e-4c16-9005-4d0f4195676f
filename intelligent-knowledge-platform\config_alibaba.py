# -*- coding: utf-8 -*-
"""
使用阿里云通义千问的配置文件
替代本地Ollama模型，简化环境搭建
"""

import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
DOCUMENTS_DIR = DATA_DIR / "documents"
VECTOR_DB_DIR = DATA_DIR / "vector_db"

# 确保目录存在
DATA_DIR.mkdir(exist_ok=True)
DOCUMENTS_DIR.mkdir(exist_ok=True)
VECTOR_DB_DIR.mkdir(exist_ok=True)

# ==================== AI模型配置 ====================

# 阿里云DashScope配置
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY", "your-api-key-here")
ALIBABA_MODEL = "qwen-turbo"  # 可选: qwen-turbo, qwen-plus, qwen-max

# 向量化配置
EMBEDDING_TYPE = "local"  # 可选: "local" 或 "alibaba"

# 本地向量化模型配置 (推荐用于学习)
LOCAL_EMBEDDING_MODEL = "all-MiniLM-L6-v2"  # 轻量级，约90MB
# 其他可选模型:
# "paraphrase-multilingual-MiniLM-L12-v2"  # 多语言支持更好，约420MB
# "text2vec-base-chinese"  # 中文优化，约400MB

# 阿里云向量化模型配置
ALIBABA_EMBEDDING_MODEL = "text-embedding-v1"  # 阿里云向量化模型

# ==================== 文本处理配置 ====================

# 文本分割配置
CHUNK_SIZE = 1000  # 每个文本块的大小
CHUNK_OVERLAP = 200  # 文本块之间的重叠

# 检索配置
TOP_K = 3  # 检索最相关的文档数量

# ==================== 模型参数配置 ====================

# 阿里模型参数
ALIBABA_CONFIG = {
    "model": ALIBABA_MODEL,
    "max_tokens": 2000,
    "temperature": 0.1,  # 降低随机性
    "top_p": 0.8,
    "repetition_penalty": 1.1
}

# ==================== 提示词模板 ====================

# 问答提示词模板
QA_PROMPT_TEMPLATE = """你是一个专业的知识问答助手。请基于以下提供的上下文信息来回答用户的问题。

上下文信息：
{context}

用户问题：{question}

请遵循以下要求：
1. 仅基于提供的上下文信息回答问题
2. 如果上下文中没有相关信息，请明确说明"根据提供的信息无法回答该问题"
3. 回答要准确、简洁、有条理
4. 如果可能，请提供具体的细节和例子

回答："""

# 文档摘要提示词模板
SUMMARY_PROMPT_TEMPLATE = """请为以下文档内容生成一个简洁的摘要：

文档内容：
{content}

请生成一个100-200字的摘要，突出文档的主要内容和关键信息："""

# ==================== 日志配置 ====================

import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# ==================== 环境检查函数 ====================

def check_environment():
    """检查环境配置是否正确"""
    issues = []
    
    # 检查API Key
    if DASHSCOPE_API_KEY == "your-api-key-here" or not DASHSCOPE_API_KEY:
        issues.append("❌ 请设置正确的DASHSCOPE_API_KEY环境变量")
    else:
        issues.append("✅ DashScope API Key已配置")
    
    # 检查目录
    if DATA_DIR.exists():
        issues.append("✅ 数据目录已创建")
    else:
        issues.append("❌ 数据目录创建失败")
    
    # 检查依赖包
    try:
        import dashscope
        issues.append("✅ dashscope包已安装")
    except ImportError:
        issues.append("❌ 请安装dashscope包: pip install dashscope")
    
    try:
        import sentence_transformers
        issues.append("✅ sentence-transformers包已安装")
    except ImportError:
        issues.append("❌ 请安装sentence-transformers包")
    
    try:
        import chromadb
        issues.append("✅ chromadb包已安装")
    except ImportError:
        issues.append("❌ 请安装chromadb包")
    
    return issues

def test_alibaba_connection():
    """测试阿里云API连接"""
    try:
        import dashscope
        from dashscope import Generation
        
        # 设置API Key
        dashscope.api_key = DASHSCOPE_API_KEY
        
        # 测试调用
        response = Generation.call(
            model=ALIBABA_MODEL,
            prompt="你好，请说一句话测试连接",
            max_tokens=50
        )
        
        if response.status_code == 200:
            return True, f"✅ 阿里云API连接成功: {response.output.text[:30]}..."
        else:
            return False, f"❌ API调用失败: {response.message}"
            
    except Exception as e:
        return False, f"❌ 连接测试失败: {str(e)}"

if __name__ == "__main__":
    print("🔧 环境配置检查")
    print("=" * 50)
    
    # 检查环境
    issues = check_environment()
    for issue in issues:
        print(issue)
    
    print("\n🌐 API连接测试")
    print("=" * 50)
    
    # 测试连接
    success, message = test_alibaba_connection()
    print(message)
    
    if success:
        print("\n🎉 环境配置完成！可以开始开发了。")
    else:
        print("\n⚠️  请解决上述问题后再继续。")
