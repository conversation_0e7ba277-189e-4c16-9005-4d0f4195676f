# -*- coding: utf-8 -*-
"""
增强版向量存储
支持本地和阿里云两种向量化方案
"""

import os
import logging
from typing import List, Dict, Any, Optional
import numpy as np
from pathlib import Path

# 导入配置
from config_alibaba import (
    EMBEDDING_TYPE,
    LOCAL_EMBEDDING_MODEL,
    ALIBABA_EMBEDDING_MODEL,
    DASHSCOPE_API_KEY,
    VECTOR_DB_DIR
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmbeddingService:
    """向量化服务，支持本地和云端两种方案"""
    
    def __init__(self, embedding_type: str = EMBEDDING_TYPE):
        self.embedding_type = embedding_type
        self.model = None
        
        if embedding_type == "local":
            self._init_local_model()
        elif embedding_type == "alibaba":
            self._init_alibaba_model()
        else:
            raise ValueError(f"不支持的向量化类型: {embedding_type}")
    
    def _init_local_model(self):
        """初始化本地向量化模型"""
        try:
            from sentence_transformers import SentenceTransformer
            
            logger.info(f"正在加载本地向量化模型: {LOCAL_EMBEDDING_MODEL}")
            self.model = SentenceTransformer(LOCAL_EMBEDDING_MODEL)
            logger.info("✅ 本地向量化模型加载成功")
            
        except ImportError:
            raise ImportError("请安装 sentence-transformers: pip install sentence-transformers")
        except Exception as e:
            logger.error(f"本地模型加载失败: {str(e)}")
            raise
    
    def _init_alibaba_model(self):
        """初始化阿里云向量化模型"""
        try:
            import dashscope
            from dashscope import TextEmbedding
            
            if DASHSCOPE_API_KEY == "your-api-key-here" or not DASHSCOPE_API_KEY:
                raise ValueError("请设置正确的DASHSCOPE_API_KEY环境变量")
            
            dashscope.api_key = DASHSCOPE_API_KEY
            self.model = TextEmbedding
            logger.info("✅ 阿里云向量化服务初始化成功")
            
        except ImportError:
            raise ImportError("请安装 dashscope: pip install dashscope")
        except Exception as e:
            logger.error(f"阿里云向量化服务初始化失败: {str(e)}")
            raise
    
    def encode(self, texts: List[str]) -> np.ndarray:
        """将文本转换为向量"""
        if self.embedding_type == "local":
            return self._encode_local(texts)
        elif self.embedding_type == "alibaba":
            return self._encode_alibaba(texts)
    
    def _encode_local(self, texts: List[str]) -> np.ndarray:
        """使用本地模型进行向量化"""
        try:
            embeddings = self.model.encode(texts, convert_to_numpy=True)
            logger.info(f"本地向量化完成: {len(texts)} 个文本 -> {embeddings.shape}")
            return embeddings
        except Exception as e:
            logger.error(f"本地向量化失败: {str(e)}")
            raise
    
    def _encode_alibaba(self, texts: List[str]) -> np.ndarray:
        """使用阿里云API进行向量化"""
        try:
            response = self.model.call(
                model=ALIBABA_EMBEDDING_MODEL,
                input=texts
            )
            
            if response.status_code == 200:
                embeddings = []
                for item in response.output['embeddings']:
                    embeddings.append(item['embedding'])
                
                embeddings = np.array(embeddings)
                logger.info(f"阿里云向量化完成: {len(texts)} 个文本 -> {embeddings.shape}")
                return embeddings
            else:
                raise Exception(f"API调用失败: {response.message}")
                
        except Exception as e:
            logger.error(f"阿里云向量化失败: {str(e)}")
            raise

class EnhancedVectorStore:
    """增强版向量存储"""
    
    def __init__(self, collection_name: str = "knowledge_base", embedding_type: str = EMBEDDING_TYPE):
        self.collection_name = collection_name
        self.embedding_service = EmbeddingService(embedding_type)
        
        # 初始化ChromaDB
        self._init_chromadb()
    
    def _init_chromadb(self):
        """初始化ChromaDB"""
        try:
            import chromadb
            from chromadb.config import Settings
            
            # 创建持久化客户端
            self.client = chromadb.PersistentClient(
                path=str(VECTOR_DB_DIR),
                settings=Settings(anonymized_telemetry=False)
            )
            
            # 获取或创建集合
            try:
                self.collection = self.client.get_collection(name=self.collection_name)
                logger.info(f"✅ 加载现有向量集合: {self.collection_name}")
            except:
                self.collection = self.client.create_collection(name=self.collection_name)
                logger.info(f"✅ 创建新向量集合: {self.collection_name}")
                
        except ImportError:
            raise ImportError("请安装 chromadb: pip install chromadb")
        except Exception as e:
            logger.error(f"ChromaDB初始化失败: {str(e)}")
            raise
    
    def add_documents(self, texts: List[str], metadatas: Optional[List[Dict]] = None):
        """添加文档到向量数据库"""
        try:
            # 生成向量
            embeddings = self.embedding_service.encode(texts)
            
            # 生成ID
            ids = [f"doc_{i}" for i in range(len(texts))]
            
            # 准备元数据
            if metadatas is None:
                metadatas = [{"source": f"document_{i}"} for i in range(len(texts))]
            
            # 添加到ChromaDB
            self.collection.add(
                embeddings=embeddings.tolist(),
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"✅ 成功添加 {len(texts)} 个文档到向量数据库")
            
        except Exception as e:
            logger.error(f"添加文档失败: {str(e)}")
            raise
    
    def similarity_search(self, query: str, k: int = 3) -> List[Dict[str, Any]]:
        """相似度搜索"""
        try:
            # 将查询转换为向量
            query_embedding = self.embedding_service.encode([query])
            
            # 在ChromaDB中搜索
            results = self.collection.query(
                query_embeddings=query_embedding.tolist(),
                n_results=k
            )
            
            # 格式化结果
            documents = []
            for i in range(len(results['documents'][0])):
                doc = {
                    'page_content': results['documents'][0][i],
                    'metadata': results['metadatas'][0][i],
                    'distance': results['distances'][0][i] if 'distances' in results else None
                }
                documents.append(doc)
            
            logger.info(f"✅ 相似度搜索完成，返回 {len(documents)} 个结果")
            return documents
            
        except Exception as e:
            logger.error(f"相似度搜索失败: {str(e)}")
            return []
    
    def get_collection_info(self) -> Dict[str, Any]:
        """获取集合信息"""
        try:
            count = self.collection.count()
            return {
                "name": self.collection_name,
                "count": count,
                "embedding_type": self.embedding_service.embedding_type
            }
        except Exception as e:
            logger.error(f"获取集合信息失败: {str(e)}")
            return {}

def test_embedding_service():
    """测试向量化服务"""
    print("🧪 测试向量化服务")
    print("=" * 50)
    
    # 测试文本
    test_texts = [
        "Python是一种编程语言",
        "机器学习是人工智能的分支",
        "向量数据库用于存储向量"
    ]
    
    # 测试本地向量化
    try:
        print("\n📍 测试本地向量化...")
        local_service = EmbeddingService("local")
        local_embeddings = local_service.encode(test_texts)
        print(f"✅ 本地向量化成功: {local_embeddings.shape}")
    except Exception as e:
        print(f"❌ 本地向量化失败: {str(e)}")
    
    # 测试阿里云向量化（如果配置了API Key）
    if DASHSCOPE_API_KEY != "your-api-key-here":
        try:
            print("\n☁️ 测试阿里云向量化...")
            alibaba_service = EmbeddingService("alibaba")
            alibaba_embeddings = alibaba_service.encode(test_texts)
            print(f"✅ 阿里云向量化成功: {alibaba_embeddings.shape}")
        except Exception as e:
            print(f"❌ 阿里云向量化失败: {str(e)}")
    else:
        print("\n⚠️ 跳过阿里云向量化测试（未配置API Key）")

if __name__ == "__main__":
    test_embedding_service()
