# 🚀 快速开始 - 阿里云模型版

> 使用阿里云通义千问，跳过本地模型搭建，快速体验AI问答系统

## 🎯 优势对比

| 方案 | 本地Ollama | 阿里云API |
|------|------------|-----------|
| **环境搭建** | 复杂，需要下载模型 | 简单，只需API Key |
| **硬件要求** | 高，需要大内存 | 低，只需网络连接 |
| **模型性能** | 受硬件限制 | 企业级高性能 |
| **稳定性** | 依赖本地环境 | 云端高可用 |
| **成本** | 硬件成本高 | 按使用量付费 |
| **学习难度** | 高 | 低 |

## 📋 前置条件

### 1. 获取阿里云API Key
1. 访问 [阿里云DashScope控制台](https://dashscope.console.aliyun.com/)
2. 注册/登录阿里云账号
3. 开通DashScope服务（有免费额度）
4. 创建API Key
5. 记录你的API Key（格式：`sk-xxxxxxxxxx`）

### 2. 基础环境
- Python 3.8+
- 网络连接

## 🛠️ 快速安装

### 步骤1：创建项目目录
```bash
mkdir intelligent-knowledge-platform
cd intelligent-knowledge-platform
```

### 步骤2：创建虚拟环境
```bash
# 使用conda
conda create -n ai-knowledge python=3.11
conda activate ai-knowledge

# 或使用venv
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 步骤3：安装依赖
```bash
pip install dashscope==1.14.1
pip install sentence-transformers==2.2.2
pip install chromadb==0.4.22
pip install langchain==0.1.0
pip install langchain-community==0.0.10
```

### 步骤4：设置API Key
```bash
# 方法1：环境变量（推荐）
export DASHSCOPE_API_KEY="your-api-key-here"

# Windows PowerShell
$env:DASHSCOPE_API_KEY="your-api-key-here"

# 方法2：直接在代码中修改 config_alibaba.py
```

## 🚀 快速体验

### 1. 下载配置文件
将以下文件保存到项目目录：
- `config_alibaba.py` - 配置文件
- `qa_chain_alibaba.py` - 问答系统

### 2. 测试环境
```bash
python config_alibaba.py
```

预期输出：
```
🔧 环境配置检查
==================================================
✅ DashScope API Key已配置
✅ 数据目录已创建
✅ dashscope包已安装
✅ sentence-transformers包已安装
✅ chromadb包已安装

🌐 API连接测试
==================================================
✅ 阿里云API连接成功: 你好！我是通义千问...

🎉 环境配置完成！可以开始开发了。
```

### 3. 运行问答系统
```bash
python qa_chain_alibaba.py
```

### 4. 开始问答
```
🤖 阿里云智能问答系统
==================================================

💬 开始问答 (输入 'quit' 退出)
------------------------------

❓ 请输入问题: Python是什么？

🤖 回答: Python是一种高级编程语言，具有简洁的语法和强大的功能。它被广泛用于Web开发、数据科学、人工智能等领域...

📚 参考文档 (1 个):
  1. Python是一种高级编程语言，具有简洁的语法和强大的功能。...
```

## 📚 进阶使用

### 1. 添加自己的文档
```python
# 修改 qa_chain_alibaba.py 中的 sample_docs
sample_docs = [
    "你的文档内容1",
    "你的文档内容2",
    # ... 更多文档
]
```

### 2. 调整模型参数
```python
# 在 config_alibaba.py 中修改
ALIBABA_CONFIG = {
    "model": "qwen-plus",  # 更强的模型
    "max_tokens": 3000,    # 更长的回答
    "temperature": 0.3,    # 更有创意的回答
}
```

### 3. 自定义提示词
```python
# 修改 config_alibaba.py 中的 QA_PROMPT_TEMPLATE
QA_PROMPT_TEMPLATE = """你是一个专业的技术顾问...
{context}
{question}
请提供详细的技术解答："""
```

## 🔧 常见问题

### Q1: API Key设置失败？
```bash
# 检查环境变量
echo $DASHSCOPE_API_KEY  # Linux/Mac
echo $env:DASHSCOPE_API_KEY  # Windows PowerShell

# 或者直接在代码中设置
DASHSCOPE_API_KEY = "sk-your-actual-key"
```

### Q2: 网络连接失败？
- 检查网络连接
- 确认防火墙设置
- 尝试使用代理

### Q3: API调用限额？
- 查看 [DashScope控制台](https://dashscope.console.aliyun.com/) 的使用情况
- 考虑升级套餐或优化调用频率

### Q4: 回答质量不满意？
- 尝试使用更强的模型：`qwen-plus` 或 `qwen-max`
- 调整 `temperature` 参数
- 优化提示词模板
- 提供更好的上下文文档

## 🎯 下一步计划

完成基础体验后，你可以：

1. **集成真实向量数据库**
   - 使用ChromaDB或Pinecone
   - 实现文档上传和向量化

2. **开发Web界面**
   - 使用FastAPI构建后端API
   - 使用React开发前端界面

3. **添加更多功能**
   - 文档管理
   - 对话历史
   - 用户认证

4. **性能优化**
   - 缓存机制
   - 异步处理
   - 负载均衡

## 💡 学习建议

1. **先跑通基础流程** - 不要纠结细节，先让系统工作起来
2. **逐步添加功能** - 一次只专注一个功能点
3. **多实验参数** - 尝试不同的模型和参数组合
4. **记录问题** - 遇到问题及时记录和解决

## 🔗 相关资源

- [阿里云DashScope文档](https://help.aliyun.com/zh/dashscope/)
- [通义千问API参考](https://help.aliyun.com/zh/dashscope/developer-reference/api-details)
- [LangChain中文文档](https://python.langchain.com.cn/)

---

**记住：学习AI应用开发最重要的是动手实践！** 🚀
