# -*- coding: utf-8 -*-
"""
使用阿里云通义千问的问答链
简化版实现，替代Ollama本地模型
"""

import os
import logging
from typing import List, Dict, Any
import dashscope
from dashscope import Generation

# 导入配置
from config_alibaba import (
    DASHSCOPE_API_KEY, 
    ALIBABA_CONFIG, 
    QA_PROMPT_TEMPLATE,
    TOP_K
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AlibabaQAChain:
    """使用阿里云通义千问的问答链"""
    
    def __init__(self, vector_store=None):
        """初始化问答链"""
        self.vector_store = vector_store
        
        # 设置API Key
        dashscope.api_key = DASHSCOPE_API_KEY
        
        # 验证API Key
        if DASHSCOPE_API_KEY == "your-api-key-here" or not DASHSCOPE_API_KEY:
            raise ValueError("请设置正确的DASHSCOPE_API_KEY环境变量")
        
        logger.info(f"✅ 阿里云问答链初始化成功，模型: {ALIBABA_CONFIG['model']}")
    
    def _call_alibaba_api(self, prompt: str) -> str:
        """调用阿里云API"""
        try:
            response = Generation.call(
                model=ALIBABA_CONFIG["model"],
                prompt=prompt,
                max_tokens=ALIBABA_CONFIG["max_tokens"],
                temperature=ALIBABA_CONFIG["temperature"],
                top_p=ALIBABA_CONFIG["top_p"],
                repetition_penalty=ALIBABA_CONFIG["repetition_penalty"]
            )
            
            if response.status_code == 200:
                return response.output.text.strip()
            else:
                logger.error(f"API调用失败: {response.message}")
                return f"抱歉，API调用失败: {response.message}"
                
        except Exception as e:
            logger.error(f"API调用异常: {str(e)}")
            return f"抱歉，服务暂时不可用: {str(e)}"
    
    def _retrieve_documents(self, question: str) -> List[str]:
        """检索相关文档"""
        if not self.vector_store:
            return []
        
        try:
            # 使用向量存储检索相关文档
            docs = self.vector_store.similarity_search(question, k=TOP_K)
            return [doc.page_content for doc in docs]
        except Exception as e:
            logger.error(f"文档检索失败: {str(e)}")
            return []
    
    def ask(self, question: str) -> Dict[str, Any]:
        """回答问题"""
        logger.info(f"收到问题: {question}")
        
        # 检索相关文档
        relevant_docs = self._retrieve_documents(question)
        
        if relevant_docs:
            # 构建带上下文的提示词
            context = "\n\n".join(relevant_docs)
            prompt = QA_PROMPT_TEMPLATE.format(
                context=context,
                question=question
            )
            logger.info(f"使用 {len(relevant_docs)} 个相关文档作为上下文")
        else:
            # 没有相关文档，直接回答
            prompt = f"请回答以下问题：{question}"
            logger.info("未找到相关文档，使用通用知识回答")
        
        # 调用阿里云API
        answer = self._call_alibaba_api(prompt)
        
        # 返回结果
        result = {
            "question": question,
            "answer": answer,
            "source_documents": relevant_docs,
            "model": ALIBABA_CONFIG["model"]
        }
        
        logger.info(f"问答完成，答案长度: {len(answer)} 字符")
        return result
    
    def chat(self, messages: List[Dict[str, str]]) -> str:
        """多轮对话"""
        # 将对话历史转换为提示词
        conversation = ""
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            if role == "user":
                conversation += f"用户: {content}\n"
            elif role == "assistant":
                conversation += f"助手: {content}\n"
        
        # 调用API
        return self._call_alibaba_api(conversation + "助手: ")

# 简化的向量存储类（用于演示）
class SimpleVectorStore:
    """简化的向量存储，用于演示"""
    
    def __init__(self):
        self.documents = []
    
    def add_documents(self, docs: List[str]):
        """添加文档"""
        self.documents.extend(docs)
        logger.info(f"添加了 {len(docs)} 个文档")
    
    def similarity_search(self, query: str, k: int = 3) -> List[Any]:
        """简单的关键词匹配搜索"""
        # 这里使用简单的关键词匹配，实际应用中应该使用向量相似度
        query_lower = query.lower()
        relevant_docs = []
        
        for doc in self.documents:
            if any(word in doc.lower() for word in query_lower.split()):
                # 创建一个简单的文档对象
                class SimpleDoc:
                    def __init__(self, content):
                        self.page_content = content
                
                relevant_docs.append(SimpleDoc(doc))
                
                if len(relevant_docs) >= k:
                    break
        
        return relevant_docs

def main():
    """主函数 - 演示使用"""
    print("🤖 阿里云智能问答系统")
    print("=" * 50)
    
    try:
        # 创建向量存储
        vector_store = SimpleVectorStore()
        
        # 添加一些示例文档
        sample_docs = [
            "Python是一种高级编程语言，具有简洁的语法和强大的功能。",
            "机器学习是人工智能的一个分支，通过算法让计算机从数据中学习。",
            "Docker是一个容器化平台，可以帮助开发者打包和部署应用程序。",
            "Redis是一个内存数据库，常用于缓存和会话存储。"
        ]
        vector_store.add_documents(sample_docs)
        
        # 创建问答链
        qa_chain = AlibabaQAChain(vector_store)
        
        # 交互式问答
        print("\n💬 开始问答 (输入 'quit' 退出)")
        print("-" * 30)
        
        while True:
            question = input("\n❓ 请输入问题: ").strip()
            
            if question.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not question:
                continue
            
            # 获取答案
            result = qa_chain.ask(question)
            
            print(f"\n🤖 回答: {result['answer']}")
            
            if result['source_documents']:
                print(f"\n📚 参考文档 ({len(result['source_documents'])} 个):")
                for i, doc in enumerate(result['source_documents'], 1):
                    print(f"  {i}. {doc[:100]}...")
    
    except Exception as e:
        print(f"❌ 系统启动失败: {str(e)}")
        print("请检查API Key配置和网络连接")

if __name__ == "__main__":
    main()
