# 阶段0：环境搭建与准备 (第0周)

## 🎯 本阶段目标
- 搭建完整的开发环境
- 熟悉Docker容器化技术
- 配置AI开发环境
- 创建项目基础结构

## 📚 需要学习的技术

### 1. Docker基础知识
- **学习资源**：
  - [Docker官方文档](https://docs.docker.com/)
  - [Docker入门教程](https://www.runoob.com/docker/docker-tutorial.html)
- **核心概念**：镜像(Image)、容器(Container)、数据卷(Volume)、网络(Network)
- **必须掌握的命令**：
  ```bash
  docker pull <镜像名>     # 拉取镜像
  docker run <镜像名>      # 运行容器
  docker ps               # 查看运行中的容器
  docker stop <容器ID>    # 停止容器
  docker-compose up -d    # 启动编排的服务
  ```

### 2. Git版本控制
- **学习资源**：
  - [Git官方教程](https://git-scm.com/book/zh/v2)
  - [廖雪峰Git教程](https://www.liaoxuefeng.com/wiki/896043488029600)
- **必须掌握的命令**：
  ```bash
  git init                # 初始化仓库
  git add .               # 添加文件到暂存区
  git commit -m "message" # 提交更改
  git push origin main    # 推送到远程仓库
  git pull                # 拉取远程更新
  ```

### 3. Python环境管理
- **学习资源**：
  - [Conda官方文档](https://docs.conda.io/)
  - [Python虚拟环境指南](https://docs.python.org/zh-cn/3/tutorial/venv.html)

## 🛠️ 环境搭建步骤

### 步骤1：安装基础工具

#### 1.1 安装Docker Desktop
```bash
# Windows用户
# 1. 下载Docker Desktop for Windows
# 2. 安装并启动Docker Desktop
# 3. 验证安装
docker --version
docker-compose --version
```

#### 1.2 安装Git
```bash
# Windows用户可以下载Git for Windows
# 验证安装
git --version
```

#### 1.3 安装Python环境
```bash
# 推荐使用Miniconda
# 1. 下载Miniconda3
# 2. 安装后创建虚拟环境
conda create -n ai-knowledge python=3.11
conda activate ai-knowledge
```

#### 1.4 安装开发工具
- **IDE**: VS Code 或 IntelliJ IDEA
- **数据库工具**: DBeaver 或 Navicat
- **API测试工具**: Postman 或 Insomnia

### 步骤2：创建项目结构

#### 2.1 创建项目目录
```bash
mkdir intelligent-knowledge-platform
cd intelligent-knowledge-platform

# 创建项目结构
mkdir -p {backend,frontend,ai-service,docs,docker,scripts}
```

#### 2.2 初始化Git仓库
```bash
git init
echo "# Intelligent Knowledge Platform" > README.md

# 创建.gitignore文件
cat > .gitignore << EOF
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
.env

# Java
target/
*.jar
*.war
*.ear
*.class

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Docker
.dockerignore

# Logs
logs/
*.log
EOF

git add .
git commit -m "Initial project structure"
```

### 步骤3：配置Docker环境

#### 3.1 创建docker-compose.yml
```yaml
# docker/docker-compose.yml
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: ik-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: knowledge_db
      MYSQL_USER: app_user
      MYSQL_PASSWORD: app_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - ik-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ik-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ik-network

  # Elasticsearch搜索引擎
  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: ik-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - es_data:/usr/share/elasticsearch/data
    networks:
      - ik-network

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: ik-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - ik-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: ik-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - ik-network

volumes:
  mysql_data:
  redis_data:
  es_data:
  minio_data:
  rabbitmq_data:

networks:
  ik-network:
    driver: bridge
```

#### 3.2 创建MySQL初始化脚本
```sql
-- docker/mysql/init/01-init.sql
CREATE DATABASE IF NOT EXISTS knowledge_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE knowledge_db;

-- 用户表
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 文档表
CREATE TABLE IF NOT EXISTS document (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    content_type VARCHAR(100),
    user_id BIGINT NOT NULL,
    status ENUM('UPLOADING', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'UPLOADING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES sys_user(id)
);

-- 插入测试用户
INSERT INTO sys_user (username, password, email) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '<EMAIL>');
-- 密码是: admin123
```

#### 3.3 启动基础环境
```bash
cd docker
docker-compose up -d

# 验证服务启动
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 步骤4：配置AI开发环境

#### 4.1 获取阿里云API密钥
```bash
# 1. 访问阿里云控制台：https://dashscope.console.aliyun.com/
# 2. 开通DashScope服务
# 3. 创建API Key
# 4. 记录你的API Key（格式类似：sk-xxxxxxxxxx）
```

#### 4.2 创建Python AI环境
```bash
# 激活虚拟环境
conda activate ai-knowledge

# 安装核心依赖
pip install langchain==0.1.0
pip install langchain-community==0.0.10
pip install chromadb==0.4.22
pip install sentence-transformers==2.2.2
pip install pypdf==3.17.4
pip install python-docx==1.1.0
pip install fastapi==0.108.0
pip install uvicorn==0.25.0
pip install python-multipart==0.0.6

# 安装阿里云SDK (替代Ollama)
pip install dashscope==1.14.1
pip install openai==1.12.0  # 兼容OpenAI接口

# 保存依赖列表
pip freeze > ai-service/requirements.txt
```

### 步骤5：验证环境

#### 5.1 验证Docker服务
```bash
# 检查MySQL连接
docker exec -it ik-mysql mysql -u root -proot123 -e "SHOW DATABASES;"

# 检查Redis连接
docker exec -it ik-redis redis-cli ping

# 检查Elasticsearch
curl http://localhost:9200/_cluster/health

# 检查MinIO (浏览器访问 http://localhost:9001)
# 用户名: minioadmin, 密码: minioadmin123

# 检查RabbitMQ (浏览器访问 http://localhost:15672)
# 用户名: admin, 密码: admin123
```

#### 5.2 验证AI环境
```python
# 创建测试脚本 test_ai_env.py
import dashscope
from sentence_transformers import SentenceTransformer
import os

# 设置API Key (请替换为你的实际API Key)
os.environ['DASHSCOPE_API_KEY'] = 'your-api-key-here'

# 测试阿里通义千问
try:
    from dashscope import Generation

    response = Generation.call(
        model='qwen-turbo',
        prompt='你好，请简单介绍一下自己',
        max_tokens=100
    )

    if response.status_code == 200:
        print("阿里模型测试成功:", response.output.text[:50])
    else:
        print("阿里模型测试失败:", response.message)
except Exception as e:
    print("阿里模型测试失败:", e)

# 测试向量化模型
try:
    model = SentenceTransformer('all-MiniLM-L6-v2')
    embeddings = model.encode(["Hello world", "你好世界"])
    print("向量化测试成功，向量维度:", embeddings.shape)
except Exception as e:
    print("向量化测试失败:", e)
```

### 步骤6：创建开发脚本

#### 6.1 创建启动脚本
```bash
# scripts/start-dev.sh (Linux/Mac)
#!/bin/bash
echo "启动开发环境..."

# 启动Docker服务
cd docker
docker-compose up -d

echo "等待服务启动..."
sleep 10

# 检查服务状态
docker-compose ps

echo "开发环境启动完成！"
echo "MySQL: localhost:3306"
echo "Redis: localhost:6379"
echo "Elasticsearch: http://localhost:9200"
echo "MinIO: http://localhost:9001"
echo "RabbitMQ: http://localhost:15672"
```

```powershell
# scripts/start-dev.ps1 (Windows)
Write-Host "启动开发环境..." -ForegroundColor Green

# 启动Docker服务
Set-Location docker
docker-compose up -d

Write-Host "等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查服务状态
docker-compose ps

Write-Host "开发环境启动完成！" -ForegroundColor Green
Write-Host "MySQL: localhost:3306" -ForegroundColor Cyan
Write-Host "Redis: localhost:6379" -ForegroundColor Cyan
Write-Host "Elasticsearch: http://localhost:9200" -ForegroundColor Cyan
Write-Host "MinIO: http://localhost:9001" -ForegroundColor Cyan
Write-Host "RabbitMQ: http://localhost:15672" -ForegroundColor Cyan
```

## 🎯 阶段完成检查清单

- [ ] Docker Desktop安装并运行正常
- [ ] Git版本控制配置完成
- [ ] Python虚拟环境创建成功
- [ ] 项目目录结构创建完成
- [ ] Docker Compose服务全部启动成功
- [ ] MySQL数据库连接正常，测试数据插入成功
- [ ] Redis缓存服务正常
- [ ] Elasticsearch搜索引擎正常
- [ ] MinIO对象存储服务正常
- [ ] RabbitMQ消息队列服务正常
- [ ] 阿里云DashScope API配置并测试成功
- [ ] Python AI依赖包安装完成
- [ ] 开发脚本创建并测试成功

## 🚀 下一步
环境搭建完成后，你就可以进入**阶段1：核心MVP开发**，开始构建你的第一个AI问答机器人！

## 💡 常见问题

### Q1: Docker启动失败怎么办？
- 检查Docker Desktop是否正常运行
- 确保端口没有被占用
- 查看docker-compose logs排查错误

### Q2: Elasticsearch启动内存不足？
- 降低ES内存配置：`ES_JAVA_OPTS=-Xms256m -Xmx256m`
- 或者暂时注释掉ES服务，后续阶段再启用

### Q3: 阿里云API调用失败？
- 检查API Key是否正确设置
- 确认DashScope服务已开通
- 检查网络连接是否正常
- 查看API调用限额是否充足

记住：**不要追求完美，先让环境跑起来！**
