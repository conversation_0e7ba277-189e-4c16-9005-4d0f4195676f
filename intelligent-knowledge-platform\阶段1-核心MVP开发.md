# 阶段1：核心MVP开发 - 命令行文档问答机器人 (1-2周)

## 🎯 本阶段目标
- 实现基于单个PDF文档的问答功能
- 掌握RAG（检索增强生成）核心流程
- 理解向量化、向量存储和检索的原理
- 完成第一个可工作的AI应用

## 📚 需要学习的技术

### 1. LangChain框架
- **学习资源**：
  - [LangChain官方文档](https://python.langchain.com/docs/get_started/introduction)
  - [LangChain中文教程](https://liaokong.gitbook.io/llm-kai-fa-jiao-cheng/)
- **核心概念**：
  - Document Loaders（文档加载器）
  - Text Splitters（文本分割器）
  - Vector Stores（向量存储）
  - Retrievers（检索器）
  - Chains（链式调用）

### 2. 向量化技术
- **学习资源**：
  - [Sentence-Transformers文档](https://www.sbert.net/)
  - [向量数据库原理](https://zhuanlan.zhihu.com/p/640392012)
- **核心概念**：
  - 文本嵌入（Text Embedding）
  - 余弦相似度（Cosine Similarity）
  - 向量检索（Vector Search）

### 3. RAG架构原理
- **学习资源**：
  - [RAG论文解读](https://arxiv.org/abs/2005.11401)
  - [RAG实战教程](https://zhuanlan.zhihu.com/p/671387647)
- **核心流程**：
  ```
  文档 → 分块 → 向量化 → 存储 → 检索 → 生成 → 回答
  ```

## 🛠️ 开发步骤

### 步骤1：项目初始化

#### 1.1 创建项目结构
```bash
cd intelligent-knowledge-platform/ai-service

# 创建目录结构
mkdir -p {src,data,models,tests,config}

# 创建核心文件
touch src/__init__.py
touch src/document_loader.py
touch src/vector_store.py
touch src/qa_chain.py
touch src/main.py
touch config/settings.py
```

#### 1.2 安装依赖
```bash
# 激活虚拟环境
conda activate ai-knowledge

# 安装核心依赖
pip install langchain==0.1.0
pip install langchain-community==0.0.10
pip install chromadb==0.4.22
pip install sentence-transformers==2.2.2
pip install pypdf==3.17.4
pip install python-docx==1.1.0
pip install ollama==0.1.7

# 更新requirements.txt
pip freeze > requirements.txt
```

### 步骤2：配置文件

#### 2.1 创建配置文件
```python
# config/settings.py
import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

# 数据目录
DATA_DIR = PROJECT_ROOT / "data"
DOCUMENTS_DIR = DATA_DIR / "documents"
VECTOR_DB_DIR = DATA_DIR / "vector_db"

# 确保目录存在
DATA_DIR.mkdir(exist_ok=True)
DOCUMENTS_DIR.mkdir(exist_ok=True)
VECTOR_DB_DIR.mkdir(exist_ok=True)

# AI模型配置
EMBEDDING_MODEL = "all-MiniLM-L6-v2"  # 轻量级向量化模型
LLM_MODEL = "qwen2:7b"  # Ollama本地模型

# 文本分割配置
CHUNK_SIZE = 1000  # 每个文本块的大小
CHUNK_OVERLAP = 200  # 文本块之间的重叠

# 检索配置
TOP_K = 3  # 检索最相关的文档数量

# Ollama配置
OLLAMA_BASE_URL = "http://localhost:11434"
```

### 步骤3：文档加载器

#### 3.1 实现文档加载功能
```python
# src/document_loader.py
from langchain.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from typing import List
import logging

from config.settings import CHUNK_SIZE, CHUNK_OVERLAP

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentLoader:
    """文档加载和处理类"""
    
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=CHUNK_SIZE,
            chunk_overlap=CHUNK_OVERLAP,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )
    
    def load_pdf(self, file_path: str) -> List[Document]:
        """加载PDF文档"""
        try:
            logger.info(f"开始加载PDF文档: {file_path}")
            
            # 使用PyPDFLoader加载PDF
            loader = PyPDFLoader(file_path)
            documents = loader.load()
            
            logger.info(f"PDF加载完成，共 {len(documents)} 页")
            
            # 分割文档
            split_docs = self.text_splitter.split_documents(documents)
            
            logger.info(f"文档分割完成，共 {len(split_docs)} 个文本块")
            
            return split_docs
            
        except Exception as e:
            logger.error(f"加载PDF文档失败: {e}")
            raise
    
    def load_text(self, text: str, metadata: dict = None) -> List[Document]:
        """加载纯文本"""
        try:
            # 创建Document对象
            doc = Document(page_content=text, metadata=metadata or {})
            
            # 分割文档
            split_docs = self.text_splitter.split_documents([doc])
            
            logger.info(f"文本分割完成，共 {len(split_docs)} 个文本块")
            
            return split_docs
            
        except Exception as e:
            logger.error(f"加载文本失败: {e}")
            raise

# 测试代码
if __name__ == "__main__":
    loader = DocumentLoader()
    
    # 测试文本加载
    test_text = """
    这是一个测试文档。它包含了多个段落和句子。
    
    第二段内容。这里有更多的文字内容，用于测试文档分割功能。
    我们需要确保分割后的文本块大小合适，既不会太大也不会太小。
    
    第三段内容。这段内容用于验证重叠功能是否正常工作。
    重叠可以确保重要信息不会在分割边界处丢失。
    """
    
    docs = loader.load_text(test_text, {"source": "test"})
    
    print(f"分割后的文档数量: {len(docs)}")
    for i, doc in enumerate(docs):
        print(f"\n--- 文档块 {i+1} ---")
        print(f"内容: {doc.page_content[:100]}...")
        print(f"元数据: {doc.metadata}")
```

### 步骤4：向量存储

#### 4.1 实现向量存储功能
```python
# src/vector_store.py
from langchain.vectorstores import Chroma
from langchain.embeddings import SentenceTransformerEmbeddings
from langchain.schema import Document
from typing import List, Optional
import logging

from config.settings import EMBEDDING_MODEL, VECTOR_DB_DIR, TOP_K

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VectorStore:
    """向量存储和检索类"""
    
    def __init__(self, collection_name: str = "knowledge_base"):
        self.collection_name = collection_name
        
        # 初始化嵌入模型
        logger.info(f"初始化嵌入模型: {EMBEDDING_MODEL}")
        self.embeddings = SentenceTransformerEmbeddings(
            model_name=EMBEDDING_MODEL
        )
        
        # 初始化向量数据库
        self.vector_db = None
        self._init_vector_db()
    
    def _init_vector_db(self):
        """初始化向量数据库"""
        try:
            self.vector_db = Chroma(
                collection_name=self.collection_name,
                embedding_function=self.embeddings,
                persist_directory=str(VECTOR_DB_DIR)
            )
            logger.info("向量数据库初始化成功")
        except Exception as e:
            logger.error(f"向量数据库初始化失败: {e}")
            raise
    
    def add_documents(self, documents: List[Document]) -> List[str]:
        """添加文档到向量数据库"""
        try:
            logger.info(f"开始向量化 {len(documents)} 个文档")
            
            # 添加文档到向量数据库
            ids = self.vector_db.add_documents(documents)
            
            # 持久化存储
            self.vector_db.persist()
            
            logger.info(f"文档向量化完成，生成 {len(ids)} 个向量")
            return ids
            
        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            raise
    
    def similarity_search(self, query: str, k: int = TOP_K) -> List[Document]:
        """相似度搜索"""
        try:
            logger.info(f"搜索查询: {query}")
            
            # 执行相似度搜索
            results = self.vector_db.similarity_search(query, k=k)
            
            logger.info(f"找到 {len(results)} 个相关文档")
            return results
            
        except Exception as e:
            logger.error(f"相似度搜索失败: {e}")
            raise
    
    def similarity_search_with_score(self, query: str, k: int = TOP_K) -> List[tuple]:
        """带相似度分数的搜索"""
        try:
            logger.info(f"搜索查询（带分数）: {query}")
            
            # 执行带分数的相似度搜索
            results = self.vector_db.similarity_search_with_score(query, k=k)
            
            logger.info(f"找到 {len(results)} 个相关文档")
            for i, (doc, score) in enumerate(results):
                logger.info(f"文档 {i+1} 相似度: {score:.4f}")
            
            return results
            
        except Exception as e:
            logger.error(f"相似度搜索失败: {e}")
            raise
    
    def get_retriever(self, k: int = TOP_K):
        """获取检索器"""
        return self.vector_db.as_retriever(search_kwargs={"k": k})
    
    def clear_collection(self):
        """清空集合"""
        try:
            # 删除现有集合
            self.vector_db.delete_collection()
            
            # 重新初始化
            self._init_vector_db()
            
            logger.info("向量数据库已清空")
            
        except Exception as e:
            logger.error(f"清空向量数据库失败: {e}")
            raise

# 测试代码
if __name__ == "__main__":
    from document_loader import DocumentLoader
    
    # 创建测试数据
    loader = DocumentLoader()
    test_text = """
    Spring Boot是一个基于Spring框架的快速开发框架。
    它简化了Spring应用的配置和部署过程。
    
    Redis是一个内存数据库，常用于缓存和会话存储。
    它支持多种数据结构，如字符串、列表、集合等。
    
    Elasticsearch是一个分布式搜索引擎。
    它基于Lucene构建，提供了强大的全文搜索功能。
    """
    
    docs = loader.load_text(test_text, {"source": "test_knowledge"})
    
    # 测试向量存储
    vector_store = VectorStore("test_collection")
    
    # 添加文档
    ids = vector_store.add_documents(docs)
    print(f"添加了 {len(ids)} 个文档")
    
    # 测试搜索
    query = "什么是Redis？"
    results = vector_store.similarity_search_with_score(query)
    
    print(f"\n查询: {query}")
    for i, (doc, score) in enumerate(results):
        print(f"\n结果 {i+1} (相似度: {score:.4f}):")
        print(doc.page_content[:200])
```

### 步骤5：问答链

#### 5.1 实现问答功能
```python
# src/qa_chain.py
from langchain.llms import Ollama
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate
import logging

from config.settings import LLM_MODEL, OLLAMA_BASE_URL
from vector_store import VectorStore

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QAChain:
    """问答链类"""
    
    def __init__(self, vector_store: VectorStore):
        self.vector_store = vector_store
        
        # 初始化LLM
        logger.info(f"初始化LLM模型: {LLM_MODEL}")
        self.llm = Ollama(
            model=LLM_MODEL,
            base_url=OLLAMA_BASE_URL,
            temperature=0.1  # 降低随机性，提高回答的一致性
        )
        
        # 创建提示模板
        self.prompt_template = self._create_prompt_template()
        
        # 创建问答链
        self.qa_chain = self._create_qa_chain()
    
    def _create_prompt_template(self) -> PromptTemplate:
        """创建提示模板"""
        template = """
你是一个专业的知识助手。请基于以下提供的上下文信息来回答用户的问题。

上下文信息：
{context}

用户问题：{question}

请注意：
1. 只基于提供的上下文信息回答问题
2. 如果上下文中没有相关信息，请明确说明
3. 回答要准确、简洁、有条理
4. 可以适当引用上下文中的原文

回答：
"""
        
        return PromptTemplate(
            template=template,
            input_variables=["context", "question"]
        )
    
    def _create_qa_chain(self) -> RetrievalQA:
        """创建问答链"""
        try:
            # 获取检索器
            retriever = self.vector_store.get_retriever()
            
            # 创建问答链
            qa_chain = RetrievalQA.from_chain_type(
                llm=self.llm,
                chain_type="stuff",  # 将所有检索到的文档合并后一起发送给LLM
                retriever=retriever,
                chain_type_kwargs={
                    "prompt": self.prompt_template,
                    "verbose": True  # 显示详细信息
                },
                return_source_documents=True  # 返回源文档
            )
            
            logger.info("问答链创建成功")
            return qa_chain
            
        except Exception as e:
            logger.error(f"创建问答链失败: {e}")
            raise
    
    def ask(self, question: str) -> dict:
        """提问并获取回答"""
        try:
            logger.info(f"用户提问: {question}")
            
            # 执行问答
            result = self.qa_chain({"query": question})
            
            # 提取回答和源文档
            answer = result["result"]
            source_docs = result["source_documents"]
            
            logger.info(f"回答生成完成，使用了 {len(source_docs)} 个源文档")
            
            return {
                "question": question,
                "answer": answer,
                "source_documents": source_docs,
                "source_count": len(source_docs)
            }
            
        except Exception as e:
            logger.error(f"问答失败: {e}")
            raise
    
    def ask_with_sources(self, question: str) -> str:
        """提问并返回格式化的回答（包含来源）"""
        try:
            result = self.ask(question)
            
            # 格式化输出
            formatted_answer = f"""
问题：{result['question']}

回答：
{result['answer']}

参考来源：
"""
            
            for i, doc in enumerate(result['source_documents'], 1):
                source = doc.metadata.get('source', '未知来源')
                content_preview = doc.page_content[:100].replace('\n', ' ')
                formatted_answer += f"{i}. 来源：{source}\n   内容预览：{content_preview}...\n\n"
            
            return formatted_answer
            
        except Exception as e:
            logger.error(f"格式化回答失败: {e}")
            raise

# 测试代码
if __name__ == "__main__":
    from document_loader import DocumentLoader
    
    # 准备测试数据
    loader = DocumentLoader()
    test_text = """
    Spring Boot是由Pivotal团队提供的全新框架，其设计目的是用来简化新Spring应用的初始搭建以及开发过程。
    该框架使用了特定的方式来进行配置，从而使开发人员不再需要定义样板化的配置。
    
    Spring Boot的主要特性包括：
    1. 创建独立的Spring应用程序
    2. 嵌入的Tomcat，无需部署WAR文件
    3. 简化Maven配置
    4. 自动配置Spring
    5. 提供生产就绪型功能，如指标，健康检查和外部配置
    6. 绝对没有代码生成和对XML没有要求配置
    
    Redis是一个开源的使用ANSI C语言编写、遵守BSD协议、支持网络、可基于内存亦可持久化的日志型、Key-Value数据库。
    Redis常用作数据库、缓存和消息中间件。它支持多种类型的数据结构，如字符串、散列、列表、集合、有序集合等。
    """
    
    docs = loader.load_text(test_text, {"source": "技术文档"})
    
    # 创建向量存储
    vector_store = VectorStore("test_qa")
    vector_store.clear_collection()  # 清空之前的数据
    vector_store.add_documents(docs)
    
    # 创建问答链
    qa_chain = QAChain(vector_store)
    
    # 测试问答
    questions = [
        "什么是Spring Boot？",
        "Spring Boot有哪些主要特性？",
        "Redis支持哪些数据结构？",
        "如何使用Docker部署应用？"  # 这个问题在文档中没有答案
    ]
    
    for question in questions:
        print("=" * 50)
        answer = qa_chain.ask_with_sources(question)
        print(answer)
```

### 步骤6：主程序

#### 6.1 创建命令行主程序
```python
# src/main.py
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from document_loader import DocumentLoader
from vector_store import VectorStore
from qa_chain import QAChain
from config.settings import DOCUMENTS_DIR
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KnowledgeBot:
    """知识问答机器人"""

    def __init__(self):
        self.document_loader = DocumentLoader()
        self.vector_store = VectorStore("main_knowledge_base")
        self.qa_chain = None
        self.loaded_documents = []

    def load_document(self, file_path: str):
        """加载文档"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            print(f"📄 正在加载文档: {file_path}")

            # 根据文件类型选择加载方法
            if file_path.lower().endswith('.pdf'):
                documents = self.document_loader.load_pdf(file_path)
            else:
                # 读取文本文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                documents = self.document_loader.load_text(
                    content,
                    {"source": os.path.basename(file_path)}
                )

            # 添加到向量数据库
            print("🔄 正在进行向量化处理...")
            self.vector_store.add_documents(documents)

            # 创建或更新问答链
            self.qa_chain = QAChain(self.vector_store)

            self.loaded_documents.append(file_path)
            print(f"✅ 文档加载完成！共处理 {len(documents)} 个文本块")

        except Exception as e:
            print(f"❌ 加载文档失败: {e}")
            logger.error(f"加载文档失败: {e}")

    def ask_question(self, question: str):
        """提问"""
        if not self.qa_chain:
            print("❌ 请先加载文档！")
            return

        try:
            print(f"\n🤔 正在思考您的问题: {question}")
            print("⏳ 请稍等...")

            result = self.qa_chain.ask(question)

            print("\n" + "="*60)
            print(f"❓ 问题: {result['question']}")
            print(f"\n🤖 回答:")
            print(result['answer'])

            if result['source_documents']:
                print(f"\n📚 参考来源 ({result['source_count']} 个):")
                for i, doc in enumerate(result['source_documents'], 1):
                    source = doc.metadata.get('source', '未知')
                    preview = doc.page_content[:150].replace('\n', ' ')
                    print(f"  {i}. 来源: {source}")
                    print(f"     内容: {preview}...")
                    print()

            print("="*60)

        except Exception as e:
            print(f"❌ 回答问题失败: {e}")
            logger.error(f"回答问题失败: {e}")

    def show_status(self):
        """显示状态"""
        print("\n📊 当前状态:")
        print(f"  已加载文档: {len(self.loaded_documents)}")
        for doc in self.loaded_documents:
            print(f"    - {os.path.basename(doc)}")
        print(f"  问答系统: {'✅ 已就绪' if self.qa_chain else '❌ 未就绪'}")

    def interactive_mode(self):
        """交互模式"""
        print("\n🚀 欢迎使用智能知识问答系统！")
        print("📖 请先加载文档，然后开始提问")
        print("\n可用命令:")
        print("  load <文件路径>  - 加载文档")
        print("  ask <问题>      - 提问")
        print("  status         - 查看状态")
        print("  help           - 显示帮助")
        print("  quit           - 退出程序")
        print("-" * 50)

        while True:
            try:
                user_input = input("\n💬 请输入命令: ").strip()

                if not user_input:
                    continue

                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break

                elif user_input.lower() == 'help':
                    print("\n📋 命令说明:")
                    print("  load <文件路径>  - 加载PDF或文本文档")
                    print("  ask <问题>      - 基于已加载文档回答问题")
                    print("  status         - 显示当前系统状态")
                    print("  help           - 显示此帮助信息")
                    print("  quit           - 退出程序")

                elif user_input.lower() == 'status':
                    self.show_status()

                elif user_input.lower().startswith('load '):
                    file_path = user_input[5:].strip()
                    if not file_path:
                        print("❌ 请指定文件路径")
                        continue

                    # 如果是相对路径，尝试在documents目录中查找
                    if not os.path.isabs(file_path):
                        full_path = DOCUMENTS_DIR / file_path
                        if full_path.exists():
                            file_path = str(full_path)

                    self.load_document(file_path)

                elif user_input.lower().startswith('ask '):
                    question = user_input[4:].strip()
                    if not question:
                        print("❌ 请输入问题")
                        continue

                    self.ask_question(question)

                else:
                    print("❌ 未知命令，输入 'help' 查看帮助")

            except KeyboardInterrupt:
                print("\n\n👋 程序被中断，再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")
                logger.error(f"交互模式错误: {e}")

def main():
    """主函数"""
    print("🤖 智能知识问答系统 v1.0")
    print("=" * 40)

    # 检查Ollama是否运行
    try:
        import ollama
        models = ollama.list()
        print(f"✅ Ollama连接正常，可用模型: {len(models['models'])} 个")
    except Exception as e:
        print(f"❌ Ollama连接失败: {e}")
        print("请确保Ollama已启动并安装了所需模型")
        return

    # 创建知识机器人
    bot = KnowledgeBot()

    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        # 批处理模式
        command = sys.argv[1]

        if command == "load" and len(sys.argv) > 2:
            file_path = sys.argv[2]
            bot.load_document(file_path)

            # 如果还有问题参数，直接回答
            if len(sys.argv) > 3:
                question = " ".join(sys.argv[3:])
                bot.ask_question(question)

        elif command == "ask" and len(sys.argv) > 2:
            question = " ".join(sys.argv[2:])
            print("❌ 请先加载文档")

        else:
            print("❌ 无效的命令行参数")
            print("用法: python main.py [load <文件路径>] [ask <问题>]")

    else:
        # 交互模式
        bot.interactive_mode()

if __name__ == "__main__":
    main()
```

### 步骤7：创建测试文档

#### 7.1 准备测试文档
```bash
# 在data/documents目录下创建测试文档
mkdir -p data/documents

# 创建测试文本文档
cat > data/documents/spring_boot_guide.txt << 'EOF'
# Spring Boot 开发指南

## 什么是Spring Boot？

Spring Boot是由Pivotal团队提供的全新框架，其设计目的是用来简化新Spring应用的初始搭建以及开发过程。该框架使用了特定的方式来进行配置，从而使开发人员不再需要定义样板化的配置。

## 主要特性

1. **创建独立的Spring应用程序** - Spring Boot可以创建独立运行的Spring应用程序
2. **嵌入式服务器** - 内嵌Tomcat、Jetty或Undertow服务器，无需部署WAR文件
3. **简化Maven配置** - 提供starter依赖，简化Maven配置
4. **自动配置** - 根据类路径自动配置Spring应用
5. **生产就绪** - 提供指标、健康检查和外部配置等生产特性
6. **无代码生成** - 不需要代码生成，也不需要XML配置

## 快速开始

### 1. 创建项目
使用Spring Initializr (https://start.spring.io/) 创建项目，或者使用IDE的Spring Boot项目模板。

### 2. 添加依赖
在pom.xml中添加Spring Boot starter依赖：

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>
```

### 3. 创建主类
```java
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 4. 创建控制器
```java
@RestController
public class HelloController {
    @GetMapping("/hello")
    public String hello() {
        return "Hello, Spring Boot!";
    }
}
```

## 配置文件

Spring Boot支持多种配置文件格式：

### application.properties
```properties
server.port=8080
spring.datasource.url=********************************
spring.datasource.username=root
spring.datasource.password=password
```

### application.yml
```yaml
server:
  port: 8080
spring:
  datasource:
    url: ********************************
    username: root
    password: password
```

## 常用注解

- `@SpringBootApplication`: 主应用类注解
- `@RestController`: REST控制器注解
- `@Service`: 服务层注解
- `@Repository`: 数据访问层注解
- `@Configuration`: 配置类注解
- `@Value`: 属性注入注解
- `@Autowired`: 自动装配注解

## 最佳实践

1. **项目结构**: 遵循标准的Maven项目结构
2. **配置管理**: 使用profile管理不同环境的配置
3. **异常处理**: 使用@ControllerAdvice进行全局异常处理
4. **日志配置**: 合理配置日志级别和输出格式
5. **测试**: 编写单元测试和集成测试
EOF

# 创建Redis相关文档
cat > data/documents/redis_guide.txt << 'EOF'
# Redis 使用指南

## Redis简介

Redis（Remote Dictionary Server）是一个开源的使用ANSI C语言编写、遵守BSD协议、支持网络、可基于内存亦可持久化的日志型、Key-Value数据库，并提供多种语言的API。

## 主要特性

1. **内存存储**: 数据存储在内存中，读写速度极快
2. **持久化**: 支持RDB和AOF两种持久化方式
3. **数据结构**: 支持多种数据结构
4. **高可用**: 支持主从复制和哨兵模式
5. **集群**: 支持分布式集群部署
6. **事务**: 支持事务操作

## 数据结构

### 1. 字符串 (String)
```redis
SET key value
GET key
INCR key
DECR key
```

### 2. 列表 (List)
```redis
LPUSH key value
RPUSH key value
LPOP key
RPOP key
LRANGE key start stop
```

### 3. 集合 (Set)
```redis
SADD key member
SREM key member
SMEMBERS key
SINTER key1 key2
```

### 4. 有序集合 (Sorted Set)
```redis
ZADD key score member
ZRANGE key start stop
ZRANK key member
```

### 5. 哈希 (Hash)
```redis
HSET key field value
HGET key field
HGETALL key
HDEL key field
```

## 在Spring Boot中使用Redis

### 1. 添加依赖
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>
```

### 2. 配置连接
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password:
    database: 0
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
```

### 3. 使用RedisTemplate
```java
@Autowired
private RedisTemplate<String, Object> redisTemplate;

public void setValue(String key, Object value) {
    redisTemplate.opsForValue().set(key, value);
}

public Object getValue(String key) {
    return redisTemplate.opsForValue().get(key);
}
```

### 4. 使用注解缓存
```java
@Cacheable(value = "user", key = "#id")
public User getUserById(Long id) {
    return userRepository.findById(id);
}

@CacheEvict(value = "user", key = "#user.id")
public void updateUser(User user) {
    userRepository.save(user);
}
```

## 性能优化

1. **合理设置过期时间**: 避免内存泄漏
2. **使用连接池**: 提高连接复用率
3. **批量操作**: 使用pipeline减少网络开销
4. **选择合适的数据结构**: 根据场景选择最优数据结构
5. **监控内存使用**: 定期清理无用数据

## 常见应用场景

1. **缓存**: 缓存热点数据，提高访问速度
2. **会话存储**: 存储用户会话信息
3. **计数器**: 实现点赞、访问量等计数功能
4. **排行榜**: 使用有序集合实现排行榜
5. **消息队列**: 使用列表实现简单的消息队列
6. **分布式锁**: 实现分布式环境下的锁机制
EOF
```

## 🚀 运行和测试

### 步骤8：运行程序

#### 8.1 启动程序
```bash
# 确保在ai-service目录下
cd intelligent-knowledge-platform/ai-service

# 激活虚拟环境
conda activate ai-knowledge

# 运行程序
python src/main.py
```

#### 8.2 测试流程
```bash
# 1. 加载文档
💬 请输入命令: load data/documents/spring_boot_guide.txt

# 2. 查看状态
💬 请输入命令: status

# 3. 开始提问
💬 请输入命令: ask 什么是Spring Boot？

💬 请输入命令: ask Spring Boot有哪些主要特性？

💬 请输入命令: ask 如何在Spring Boot中配置数据源？

# 4. 加载更多文档
💬 请输入命令: load data/documents/redis_guide.txt

# 5. 测试跨文档问答
💬 请输入命令: ask Redis支持哪些数据结构？

💬 请输入命令: ask 如何在Spring Boot中使用Redis缓存？
```

## 🎯 阶段完成检查清单

- [ ] Python虚拟环境激活成功
- [ ] 所有依赖包安装完成
- [ ] 项目目录结构创建正确
- [ ] 配置文件设置完成
- [ ] 文档加载器功能正常
- [ ] 向量存储功能正常
- [ ] 问答链功能正常
- [ ] 主程序运行成功
- [ ] 能够加载PDF和文本文档
- [ ] 能够进行基本问答
- [ ] 能够显示答案来源
- [ ] 交互模式工作正常

## 🎉 学习成果

完成这个阶段后，你将掌握：

1. **RAG核心原理**: 理解检索增强生成的完整流程
2. **LangChain框架**: 掌握文档处理、向量化、检索等核心功能
3. **向量数据库**: 理解向量存储和相似度搜索原理
4. **大模型应用**: 学会本地部署和调用大模型
5. **Python项目结构**: 掌握模块化开发和项目组织

## 🚀 下一步

恭喜！你已经完成了第一个AI应用！接下来进入**阶段2：Web应用开发**，我们将为这个命令行程序套上Web服务的外壳，实现前后端分离的架构。

## 💡 常见问题

### Q1: Ollama连接失败？
- 确保Ollama服务已启动
- 检查模型是否已下载：`ollama list`
- 尝试重新拉取模型：`ollama pull qwen2:7b`

### Q2: 向量化速度慢？
- 首次下载模型会比较慢，后续会使用缓存
- 可以使用更小的模型如`all-MiniLM-L6-v2`

### Q3: 内存不足？
- 减少文档分块大小：`CHUNK_SIZE = 500`
- 使用更小的向量化模型
- 限制检索文档数量：`TOP_K = 2`

记住：**先让功能跑起来，再考虑优化！**
